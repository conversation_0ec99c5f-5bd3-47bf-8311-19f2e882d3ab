/* ============================================ */
/* Services Pages Styles                       */
/* ============================================ */
/* Service Content Section */
.service-content {
  padding: var(--sectionPadding);
  background-color: #ffffff;
}
.service-content .cs-container {
  width: 100%;
  max-width: 80rem;
  margin: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 3rem;
}
.service-content .cs-content {
  text-align: left;
  width: 100%;
  max-width: 50rem;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.service-content .cs-topper {
  font-size: var(--topperFontSize);
  line-height: 1.2em;
  text-transform: uppercase;
  text-align: inherit;
  letter-spacing: 0.1em;
  font-weight: 700;
  color: var(--primary);
  margin-bottom: 0.25rem;
  display: block;
}
.service-content .cs-title {
  font-size: var(--headerFontSize);
  font-weight: 900;
  line-height: 1.2em;
  text-align: inherit;
  max-width: 43.75rem;
  margin: 0 0 1rem 0;
  color: var(--headerColor);
  position: relative;
}
.service-content .cs-text {
  font-size: var(--bodyFontSize);
  line-height: 1.5em;
  text-align: inherit;
  width: 100%;
  max-width: 40.625rem;
  margin: 0 0 1rem 0;
  color: var(--bodyTextColor);
}
.service-content .cs-text:last-of-type {
  margin-bottom: 2rem;
}
.service-content .cs-ul {
  width: 100%;
  margin: 0 0 1.5rem 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 0.75rem;
}
.service-content .cs-li {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}
.service-content .cs-li:before {
  /* bullet */
  content: "";
  width: 0.5rem;
  height: 0.5rem;
  background: var(--primary);
  border-radius: 50%;
  display: block;
  /* prevents flexbox from squishing it */
  flex: none;
  margin-top: 0.5rem;
}
.service-content .cs-li-text {
  font-size: var(--bodyFontSize);
  line-height: 1.5em;
  text-align: inherit;
  width: 100%;
  margin: 0;
  color: var(--bodyTextColor);
}
.service-content .cs-button-solid {
  font-size: 1rem;
  font-weight: 700;
  /* 46px - 56px */
  line-height: clamp(2.875rem, 5.5vw, 3.5rem);
  text-decoration: none;
  text-align: center;
  margin: 0;
  color: var(--bodyTextColorWhite);
  min-width: 9.375rem;
  padding: 0 1.5rem;
  background-color: var(--primary);
  border-radius: 0.25rem;
  display: inline-block;
  position: relative;
  z-index: 1;
  /* prevents padding from adding to the width */
  box-sizing: border-box;
  transition: color 0.3s;
}
.service-content .cs-button-solid:before {
  content: "";
  position: absolute;
  height: 100%;
  width: 0%;
  background: #000;
  opacity: 1;
  top: 0;
  left: 0;
  z-index: -1;
  border-radius: 0.25rem;
  transition: width 0.3s;
}
.service-content .cs-button-solid:hover:before {
  width: 100%;
}

/* Highlight Box */
.service-highlight {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primaryLight) 100%);
  border-radius: 1rem;
  padding: 2rem;
  margin: 2rem 0;
  text-align: center;
  box-shadow: 0 8px 32px rgba(52, 152, 219, 0.2);
}
.service-highlight .cs-highlight-title {
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1.2em;
  margin: 0 0 1rem 0;
  color: var(--bodyTextColorWhite);
}
.service-highlight .cs-highlight-text {
  font-size: 1.125rem;
  line-height: 1.5em;
  margin: 0;
  color: var(--bodyTextColorWhite);
  opacity: 0.9;
}

/* Tablet - 768px */
@media only screen and (min-width: 48rem) {
  .service-content .cs-container {
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
  }
  .service-content .cs-content {
    width: 48%;
    max-width: 33.875rem;
  }
}
/* Dark Mode */
@media only screen and (min-width: 0rem) {
  body.dark-mode .service-content {
    background-color: var(--dark);
  }
  body.dark-mode .service-content .cs-topper {
    color: var(--primaryLight);
  }
  body.dark-mode .service-content .cs-title,
  body.dark-mode .service-content .cs-text,
  body.dark-mode .service-content .cs-li-text {
    color: var(--bodyTextColorWhite);
  }
  body.dark-mode .service-content .cs-text {
    opacity: 0.8;
  }
  body.dark-mode .service-highlight {
    background: linear-gradient(135deg, var(--accent) 0%, var(--primary) 100%);
  }
}/*# sourceMappingURL=services.css.map */