/* PAGE-SPECIFIC STYLES FOR THE HOME PAGE */

/*-- -------------------------- -->
<---       Side By Side         -->
<--- -------------------------- -*/

/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
    #sbs {
        padding: var(--sectionPadding);

        .cs-container {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            margin: auto;
            max-width: calc(1280 / 16 * 1rem);
            width: 100%;
            row-gap: calc(40 / 16 * 1rem);
        }

        .cs-left {
            position: relative;
            height: calc(636 / 16 * 1em);
            /* using ems so we can use font size to scale the whole section */
            width: calc(631 / 16 * 1em);
            /* scaling the font size with the view width */
            font-size: min(2.31vw, 0.7em);
        }

        .cs-picture {
            position: absolute;
            display: block;
            border-radius: calc(24 / 16 * 1em);
            /* clips img tag corners */
            overflow: hidden;
            img {
                position: absolute;
                top: 0;
                left: 0;
                height: 100%;
                width: 100%;
                /* makes image act like a background image */
                object-fit: cover;
            }
        }

        .cs-picture1 {
            top: 0;
            left: 0;
            height: calc(581 / 16 * 1em);
            width: calc(522 / 16 * 1em);
        }

        .cs-picture2 {
            bottom: 0;
            right: 0;
            /* 6px - 12px */
            border: clamp(0.375em, 1.5vw, 0.75em) solid #fff;
            background-color: #fff;
            height: calc(400 / 16 * 1em);
            width: calc(414 / 16 * 1em);
            box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 40px;
        }

        .cs-right {
            margin: auto;
            max-width: calc(542 / 16 * 1rem);
        }

        .cs-topper {
            margin-bottom: calc(4 / 16 * 1rem);
            text-align: left;
        }

        .cs-title {
            max-width: calc(800 / 16 * 1rem);
            text-align: left;
        }

        .cs-text {
            margin-bottom: calc(16 / 16 * 1rem);
            max-width: calc(750 / 16 * 1rem);
            text-align: left;

            &:last-of-type {
                margin-bottom: calc(32 / 16 * 1rem);
            }
        }

        .cs-flex-group {
            position: relative;
            border-radius: calc(16 / 16 * 1rem);
            background-color: #f7f7f7;
            /* 16px - 32px */
            padding: clamp(1rem, 3vw, 2rem);
        }

        .cs-flex-p {
            margin: 0 0 calc(16 / 16 * 1rem);
            /* 14px - 16px */
            font-size: clamp(0.875rem, 1.5vw, 1rem);
            line-height: 1.5em;
            color: #353535;
        }

        .cs-name {
            display: block;
            margin: 0 0 calc(4 / 16 * 1rem);
            text-transform: uppercase;
            font-size: calc(16 / 16 * 1rem);
            line-height: 1.2em;
            font-weight: bold;
            color: var(--headerColor);
        }

        .cs-job {
            display: block;
            font-size: calc(14 / 16 * 1rem);
            line-height: 1.5em;
            color: #353535;
        }

        .cs-quote-icon {
            position: absolute;
            bottom: calc(0 / 16 * 1rem);
            /* 16px - 32px */
            right: clamp(1rem, 4vw, 2rem);
            height: auto;
            /* 60px - 136px */
            width: clamp(3.75rem, 10vw, 8.5rem);
        }

        .cs-button-solid {
            margin-top: calc(32 / 16 * 1rem);
        }
    }
}

/* Desktop - 1024px */
@media only screen and (min-width: 64rem) {
    #sbs {
        .cs-container {
            flex-flow: row;
            justify-content: space-between;
            gap: calc(52 / 16 * 1rem);
        }

        .cs-left {
            font-size: min(1.2vw, 1em);
            flex: none;
        }

        .cs-right {
            margin: 0;
        }
    }
}

/* Dark Mode */
@media only screen and (min-width: 0rem) {
    body.dark-mode {
        #sbs {
            .cs-left {
                &:before,
                &:after {
                    background: var(--accent);
                }
            }

            .cs-picture2 {
                /* 6px - 12px */
                border: clamp(0.375em, 1.5vw, 0.75em) solid var(--dark);
                background-color: var(--dark);
            }

            .cs-topper {
                color: var(--primaryLight);
            }

            .cs-title,
            .cs-text,
            .cs-h3,
            .cs-flex-p,
            .cs-name {
                color: var(--bodyTextColorWhite);
            }

            .cs-flex-group {
                background-color: var(--accent);
            }

            .cs-job {
                opacity: 0.8;
                color: var(--bodyTextColorWhite);
            }

            .cs-quote-icon {
                opacity: 0.2;
            }
        }
    }
}

/*-- -------------------------- -->
<---          GALLERY           -->
<--- -------------------------- -*/
@media only screen and (min-width: 0rem) {
    #gallery-2297 {
        padding: var(--sectionPadding);
        overflow: hidden;
        position: relative;
        z-index: 1;
    }

    #gallery-2297 .cs-container {
        width: 100%;
        max-width: 80rem;
        margin: auto;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        position: relative;
        z-index: 1;
    }

    #gallery-2297 .cs-content {
        width: 100%;
        display: contents;
    }

    #gallery-2297 .cs-title {
        margin: 0 0 3rem;
    }

    #gallery-2297 .cs-text {
        max-width: 50.625rem;
    }

    #gallery-2297 .cs-button-group {
        margin: 2rem auto 0;
        display: flex;
        order: 2;
        column-gap: clamp(1.25rem, 3.4vw, 2.5rem);
        row-gap: 1rem;
    }

    #gallery-2297 .cs-button {
        padding: 1rem;
        background-color: #f3f3f3;
        border: none;
        transition: background-color 0.3s;
    }

    #gallery-2297 .cs-button:hover {
        background-color: var(--primary);
        cursor: pointer;
    }

    #gallery-2297 .cs-button:hover .cs-arrow {
        filter: invert(1) brightness(1000%);
    }

    #gallery-2297 .cs-arrow {
        width: 1.25rem;
        height: 1.25rem;
        display: block;
    }

    #gallery-2297 .cs-gallery-wrapper {
        width: 100%;
        position: relative;
        z-index: 1;
    }

    #gallery-2297 .cs-gallery {
        width: 100%;
        margin: 0;
        padding: 0;
        display: grid;
        grid-template-columns: repeat(12, 1fr);
        gap: clamp(1rem, 1.5vw, 1.25rem);
        position: relative;
        transform-style: preserve-3d;
        perspective: 700px;
        transition: transform 0.7s, opacity 0.3s, visibility 0.5s, top 0.3s, left 0.3s;
        transform-origin: left top;
    }

    #gallery-2297 .cs-gallery.cs-hidden {
        pointer-events: none;
        opacity: 0;
        visibility: hidden;
        position: absolute;
        top: 0;
        left: 0;
        transform: scaleY(0) scaleX(0);
    }

    #gallery-2297 .cs-gallery.cs-hidden .cs-image {
        opacity: 0;
        transform: translateY(2.1875rem) rotateX(90deg);
    }

    #gallery-2297 .cs-image {
        min-height: clamp(20rem, 20vw, 32.75rem);
        overflow: hidden;
        opacity: 1;
        display: block;
        grid-column: span 12;
        position: relative;
        transform: translateY(0rem) rotateX(0);
        transition: opacity 0.6s, transform 0.6s;
    }

    #gallery-2297 .cs-image:nth-of-type(1) {
        transition-delay: 0.1s;
    }

    #gallery-2297 .cs-image:nth-of-type(2) {
        transition-delay: 0.2s;
    }

    #gallery-2297 .cs-image:nth-of-type(3) {
        transition-delay: 0.3s;
    }

    #gallery-2297 .cs-picture {
        width: 100%;
        height: 100%;
        background-color: #000;
        object-fit: cover;
        position: absolute;
        top: 0;
        left: 0;
    }

    #gallery-2297 .cs-picture img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        position: absolute;
        top: 0;
        left: 0;
        transition: transform 0.65s, opacity 0.3s;
    }

    #gallery-2297 .cs-info {
        width: 90%;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
        position: absolute;
        bottom: 0;
        left: 0;
        z-index: 10;
    }

    #gallery-2297 .cs-tag {
        font-size: clamp(0.8125rem, 2vw, 1rem);
        font-weight: 700;
        text-transform: uppercase;
        padding: 0.5rem 1.25rem;
        background-color: var(--primary);
        color: var(--bodyTextColorWhite);
    }

    #gallery-2297 .cs-project {
        font-size: 1.25rem;
        font-weight: 700;
        text-transform: uppercase;
        padding: 0.75rem 1rem;
        background-color: #fff;
        color: var(--headerColor);
    }

    #gallery-2297 .cs-stats {
        max-width: 80rem;
        margin: clamp(3rem, 9vw, 3.5rem) auto 0;
        display: flex;
        flex-direction: column;
        column-gap: clamp(1rem, 3vw, 1.5rem);
        row-gap: 2rem;
    }

    #gallery-2297 .cs-stat {
        display: flex;
        flex-direction: column;
        column-gap: 1.25rem;
        row-gap: 0.5rem;
    }

    #gallery-2297 .cs-number {
        font-size: var(--headerFontSize);
        font-weight: 700;
        line-height: 1.2em;
        color: var(--primary);
    }

    #gallery-2297 .cs-desc {
        font-size: 1.25rem;
        font-weight: 700;
        line-height: 1.2em;
        color: var(--headerColor);
    }
}

@media only screen and (min-width: 48rem) {
    #gallery-2297 .cs-container {
        gap: clamp(3rem, 6vw, 4rem);
    }

    #gallery-2297 .cs-content {
        text-align: left;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        gap: 1.5rem;
    }

    #gallery-2297 .cs-title {
        margin: 0;
    }

    #gallery-2297 .cs-button-group {
        margin: 0;
    }

    #gallery-2297 .cs-image {
        grid-column: span 4;
    }

    #gallery-2297 .cs-stats {
        flex-direction: row;
    }

    #gallery-2297 .cs-stat {
        flex: 1;
    }
}

@media only screen and (min-width: 64rem) {
    #gallery-2297 .cs-content {
        flex-direction: row;
        justify-content: space-between;
        align-items: flex-end;
        gap: 4rem;
    }

    #gallery-2297 .cs-image:hover .cs-tag,
    #gallery-2297 .cs-image:hover .cs-project {
        opacity: 1;
        transform: translateX(0);
    }

    #gallery-2297 .cs-image:hover .cs-picture img {
        opacity: 0.2;
        transform: scale(1.1);
    }

    #gallery-2297 .cs-tag {
        opacity: 0;
        transform: translateX(-100%);
        transition: transform 0.3s ease-in-out, opacity 0.3s;
    }

    #gallery-2297 .cs-project {
        opacity: 0;
        transform: translateX(-100%);
        transition: transform 0.4s ease-out, opacity 0.3s;
        transition-delay: 0.1s;
    }
}

@media only screen and (min-width: 81.25rem) {
    #gallery-2297 .cs-stat {
        flex-direction: row;
        align-items: center;
    }
}