/* ============================================ */
/* Services Pages Styles                       */
/* ============================================ */

/* Service Content Section */
.service-content {
    padding: var(--sectionPadding);
    background-color: #ffffff;

    .cs-container {
        width: 100%;
        max-width: calc(1280 / 16 * 1rem);
        margin: auto;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: calc(48 / 16 * 1rem);
    }

    .cs-content {
        text-align: left;
        width: 100%;
        max-width: calc(800 / 16 * 1rem);
        display: flex;
        flex-direction: column;
        align-items: flex-start;
    }

    .cs-topper {
        font-size: var(--topperFontSize);
        line-height: 1.2em;
        text-transform: uppercase;
        text-align: inherit;
        letter-spacing: 0.1em;
        font-weight: 700;
        color: var(--primary);
        margin-bottom: calc(4 / 16 * 1rem);
        display: block;
    }

    .cs-title {
        font-size: var(--headerFontSize);
        font-weight: 900;
        line-height: 1.2em;
        text-align: inherit;
        max-width: calc(700 / 16 * 1rem);
        margin: 0 0 calc(16 / 16 * 1rem) 0;
        color: var(--headerColor);
        position: relative;
    }

    .cs-text {
        font-size: var(--bodyFontSize);
        line-height: 1.5em;
        text-align: inherit;
        width: 100%;
        max-width: calc(650 / 16 * 1rem);
        margin: 0 0 calc(16 / 16 * 1rem) 0;
        color: var(--bodyTextColor);

        &:last-of-type {
            margin-bottom: calc(32 / 16 * 1rem);
        }
    }

    .cs-ul {
        width: 100%;
        margin: 0 0 calc(24 / 16 * 1rem) 0;
        padding: 0;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: calc(12 / 16 * 1rem);
    }

    .cs-li {
        list-style: none;
        padding: 0;
        margin: 0;
        display: flex;
        align-items: flex-start;
        gap: calc(12 / 16 * 1rem);

        &:before {
            /* bullet */
            content: "";
            width: calc(8 / 16 * 1rem);
            height: calc(8 / 16 * 1rem);
            background: var(--primary);
            border-radius: 50%;
            display: block;
            /* prevents flexbox from squishing it */
            flex: none;
            margin-top: calc(8 / 16 * 1rem);
        }
    }

    .cs-li-text {
        font-size: var(--bodyFontSize);
        line-height: 1.5em;
        text-align: inherit;
        width: 100%;
        margin: 0;
        color: var(--bodyTextColor);
    }

    .cs-button-solid {
        font-size: calc(16 / 16 * 1rem);
        font-weight: 700;
        /* 46px - 56px */
        line-height: clamp(2.875rem, 5.5vw, 3.5rem);
        text-decoration: none;
        text-align: center;
        margin: 0;
        color: var(--bodyTextColorWhite);
        min-width: calc(150 / 16 * 1rem);
        padding: 0 calc(24 / 16 * 1rem);
        background-color: var(--primary);
        border-radius: calc(4 / 16 * 1rem);
        display: inline-block;
        position: relative;
        z-index: 1;
        /* prevents padding from adding to the width */
        box-sizing: border-box;
        transition: color 0.3s;

        &:before {
            content: "";
            position: absolute;
            height: 100%;
            width: 0%;
            background: #000;
            opacity: 1;
            top: 0;
            left: 0;
            z-index: -1;
            border-radius: calc(4 / 16 * 1rem);
            transition: width 0.3s;
        }

        &:hover {
            &:before {
                width: 100%;
            }
        }
    }
}

/* Highlight Box */
.service-highlight {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primaryLight) 100%);
    border-radius: calc(16 / 16 * 1rem);
    padding: calc(32 / 16 * 1rem);
    margin: calc(32 / 16 * 1rem) 0;
    text-align: center;
    box-shadow: 0 8px 32px rgba(52, 152, 219, 0.2);

    .cs-highlight-title {
        font-size: calc(24 / 16 * 1rem);
        font-weight: 700;
        line-height: 1.2em;
        margin: 0 0 calc(16 / 16 * 1rem) 0;
        color: var(--bodyTextColorWhite);
    }

    .cs-highlight-text {
        font-size: calc(18 / 16 * 1rem);
        line-height: 1.5em;
        margin: 0;
        color: var(--bodyTextColorWhite);
        opacity: 0.9;
    }
}

/* Tablet - 768px */
@media only screen and (min-width: 48rem) {
    .service-content {
        .cs-container {
            flex-direction: row;
            justify-content: space-between;
            align-items: flex-start;
        }

        .cs-content {
            width: 48%;
            max-width: calc(542 / 16 * 1rem);
        }
    }
}

/* Dark Mode */
@media only screen and (min-width: 0rem) {
    body.dark-mode {
        .service-content {
            background-color: var(--dark);

            .cs-topper {
                color: var(--primaryLight);
            }

            .cs-title,
            .cs-text,
            .cs-li-text {
                color: var(--bodyTextColorWhite);
            }

            .cs-text {
                opacity: 0.8;
            }
        }

        .service-highlight {
            background: linear-gradient(135deg, var(--accent) 0%, var(--primary) 100%);
        }
    }
}
