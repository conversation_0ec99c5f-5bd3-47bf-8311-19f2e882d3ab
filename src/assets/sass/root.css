/* CODESTITCH STYLES, RESET, HEADER/NAVIGATION AND FOOTER */
/*-- -------------------------- -->
<---        Core Styles         -->
<--- -------------------------- -*/
/* CodeStitch Root and Helpers */
@media only screen and (min-width: 0rem) {
  :root {
    --primary: #3498db;
    --primaryLight: #3498db;
    --secondary: #001f3f;
    --secondaryLight: #001f3f;
    --headerColor: #1a1a1a;
    --bodyTextColor: #4e4b66;
    --bodyTextColorWhite: #fafbfc;
    /* 13px - 16px */
    --topperFontSize: clamp(0.8125rem, 1.6vw, 1rem);
    /* 31px - 49px */
    --headerFontSize: clamp(1.9375rem, 3.9vw, 3.0625rem);
    --bodyFontSize: 1rem;
    /* 60px - 100px top and bottom */
    --sectionPadding: clamp(3.75rem, 7.82vw, 6.25rem) 1rem;
  }
  .cs-topper {
    display: block;
    margin-bottom: 0.25rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    font-size: var(--topperFontSize);
    line-height: 1.2em;
    font-weight: 700;
    color: var(--primary);
    text-align: inherit;
  }
  .cs-title {
    position: relative;
    margin: 0 0 1rem 0;
    max-width: 43.75rem;
    font-size: var(--headerFontSize);
    line-height: 1.2em;
    font-weight: 900;
    color: var(--headerColor);
    text-align: inherit;
  }
  .cs-text {
    margin: 0;
    max-width: 40.625rem;
    width: 100%;
    font-size: var(--bodyFontSize);
    line-height: 1.5em;
    color: var(--bodyTextColor);
    text-align: inherit;
  }
  .cs-button-solid {
    z-index: 1;
    position: relative;
    display: inline-block;
    background-color: var(--primary);
    width: auto;
    padding: 0 1.875rem;
    text-decoration: none;
    text-transform: uppercase;
    font-size: 1rem;
    line-height: 3.125em;
    font-weight: bold;
    color: #000;
    transition: color 0.3s;
    transition-delay: 0.1s;
    text-align: center;
  }
  .cs-button-solid:hover {
    color: #fff;
  }
  .cs-button-solid:hover:before {
    width: 100%;
  }
  .cs-button-solid:before {
    z-index: -1;
    position: absolute;
    top: 0;
    left: 0;
    content: "";
    opacity: 1;
    display: block;
    background-color: #000;
    height: 100%;
    width: 0;
    transition: width 0.3s;
  }
  .cs-hide-on-mobile {
    display: none;
  }
}
/* Desktop - 1024px */
@media only screen and (min-width: 64rem) {
  .cs-hide-on-mobile {
    display: block;
  }
  .cs-hide-on-desktop {
    display: none;
  }
}
/* Fonts and general styles */
@media only screen and (min-width: 0rem) {
  body,
  html {
    margin: 0;
    overflow-x: hidden;
    padding: 0;
    font-family: "Maven Pro", Arial, sans-serif;
    font-size: 100%;
    color: var(--bodyTextColor);
  }
  *,
  *:before,
  *:after {
    margin: 0;
    box-sizing: border-box;
    padding: 0;
  }
  body {
    transition: background-color 0.3s;
  }
  .container {
    position: relative;
    margin: auto;
    width: 92%;
  }
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    margin: 0;
    color: var(--headerColor);
  }
  p,
  li,
  a {
    margin: 0;
    font-size: 1rem;
    line-height: 1.5em;
  }
  p,
  li {
    color: #353535;
  }
  a:hover,
  button:hover {
    cursor: pointer;
  }
  .skip {
    z-index: -1111111;
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
  }
  /* maven-pro-regular - latin */
  @font-face {
    font-display: swap;
    /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: "Maven Pro";
    font-style: normal;
    font-weight: 400;
    src: url("../fonts/maven-pro-v39-latin-regular.woff2") format("woff2");
    /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
  }
  /* maven-pro-700 - latin */
  @font-face {
    font-display: swap;
    /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: "Maven Pro";
    font-style: normal;
    font-weight: 700;
    src: url("../fonts/maven-pro-v39-latin-700.woff2") format("woff2");
    /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
  }
  /* maven-pro-900 - latin */
  @font-face {
    font-display: swap;
    /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: "Maven Pro";
    font-style: normal;
    font-weight: 900;
    src: url("../fonts/maven-pro-v39-latin-900.woff2") format("woff2");
    /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
  }
}
/* Reset Margins */
@media only screen and (min-width: 1024px) {
  body,
  html {
    margin: 0;
    padding: 0;
  }
}
/* Scale full website with the viewport width */
@media only screen and (min-width: 3000px) {
  body,
  html {
    font-size: 0.55vw;
  }
}
/*-- -------------------------- -->
<---     Mobile Navigation      -->
<--- -------------------------- -*/
/* Mobile - 1023px */
@media only screen and (max-width: 63.9375rem) {
  body.cs-open {
    overflow: hidden;
  }
  #cs-navigation {
    z-index: 10000;
    position: fixed;
    background-color: #fff;
    width: 100%;
    /* prevents padding from affecting height and width */
    box-sizing: border-box;
    padding: 0.75rem 1rem;
    /* remove the font family so the Stitch inherits the fonts from your global stylesheet */
    font-family: "Maven Pro", "Arial", sans-serif;
    box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
  }
  #cs-navigation .cs-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    width: 100%;
  }
  #cs-navigation .cs-logo {
    z-index: 10;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto 0 0;
    height: 100%;
    max-width: 9.125rem;
    width: 40%;
    /* prevents padding from affecting height and width */
    box-sizing: border-box;
    padding: 0;
  }
  #cs-navigation .cs-logo img {
    height: 100%;
    width: 100%;
    /* ensures the image never overflows the container. It stays contained within it's width and height and expands to fill it then stops once it reaches an edge */
    -o-object-fit: contain;
       object-fit: contain;
    /* places the image to the left edge of the parent */
    -o-object-position: left;
       object-position: left;
  }
  #cs-navigation .cs-toggle {
    display: flex;
    justify-content: center;
    align-items: center;
    border: none;
    border-radius: 0.25rem;
    margin: 0 0 0 auto;
    background-color: transparent;
    height: clamp(2.75rem, 6vw, 3rem);
    /* 44px - 48px */
    width: clamp(2.75rem, 6vw, 3rem);
  }
  #cs-navigation .cs-active .cs-line1 {
    top: 50%;
    transform: translate(-50%, -50%) rotate(225deg);
  }
  #cs-navigation .cs-active .cs-line2 {
    top: 50%;
    transform: translate(-50%, -50%) translateY(0) rotate(-225deg);
    transform-origin: center;
  }
  #cs-navigation .cs-active .cs-line3 {
    bottom: 100%;
    opacity: 0;
  }
  #cs-navigation .cs-box {
    position: relative;
    /* 14px - 16px */
    height: clamp(0.875rem, 1.5vw, 1rem);
    /* 24px - 28px */
    width: clamp(1.5rem, 2vw, 1.75rem);
  }
  #cs-navigation .cs-line {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 2px;
    background-color: #1a1a1a;
    height: 2px;
    width: 100%;
  }
  #cs-navigation .cs-line1 {
    top: 0;
    transition: transform 0.5s, top 0.3s, left 0.3s;
    animation-duration: 0.7s;
    animation-timing-function: ease;
    animation-direction: normal;
    animation-fill-mode: forwards;
    transform-origin: center;
  }
  #cs-navigation .cs-line2 {
    top: 50%;
    transform: translateX(-50%) translateY(-50%);
    transition: top 0.3s, left 0.3s, transform 0.5s;
    animation-duration: 0.7s;
    animation-timing-function: ease;
    animation-direction: normal;
    animation-fill-mode: forwards;
  }
  #cs-navigation .cs-line3 {
    bottom: 0;
    transition: bottom 0.3s, opacity 0.3s;
  }
  #cs-navigation .cs-ul-wrapper {
    z-index: -1;
    position: absolute;
    top: 100%;
    left: auto;
    right: 0;
    opacity: 0;
    visibility: hidden;
    transform: scaleX(0);
    background-color: #fff;
    height: 100vh;
    overflow: hidden;
    transition: transform 0.4s, opacity 0.3s;
    box-shadow: inset rgba(0, 0, 0, 0.2) 0px 8px 24px;
    transform-origin: top right;
  }
  #cs-navigation .cs-ul {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-end;
    margin: 0;
    height: 65vh;
    min-width: 40%;
    width: auto;
    overflow: scroll;
    /* 28px - 40px */
    padding: 3rem clamp(1.75rem, 3vw, 2.5rem) 2rem 4.375rem;
    gap: 1.25rem;
  }
  #cs-navigation .cs-li {
    opacity: 0;
    /* transition from these values */
    transform: translateX(-2.5rem);
    margin-right: 0;
    width: 100%;
    list-style: none;
    transition: transform 0.6s, opacity 0.9s;
    text-align: right;
  }
  #cs-navigation .cs-li:nth-of-type(1) {
    transition-delay: 0.05s;
  }
  #cs-navigation .cs-li:nth-of-type(2) {
    transition-delay: 0.1s;
  }
  #cs-navigation .cs-li:nth-of-type(3) {
    transition-delay: 0.15s;
  }
  #cs-navigation .cs-li:nth-of-type(4) {
    transition-delay: 0.2s;
  }
  #cs-navigation .cs-li:nth-of-type(5) {
    transition-delay: 0.25s;
  }
  #cs-navigation .cs-li:nth-of-type(6) {
    transition-delay: 0.3s;
  }
  #cs-navigation .cs-li:nth-of-type(7) {
    transition-delay: 0.35s;
  }
  #cs-navigation .cs-li:nth-of-type(8) {
    transition-delay: 0.4s;
  }
  #cs-navigation .cs-li:nth-of-type(9) {
    transition-delay: 0.45s;
  }
  #cs-navigation .cs-li-link {
    position: relative;
    display: inline-block;
    margin: 0;
    text-decoration: none;
    /* 16px - 24px */
    font-size: clamp(1rem, 2.5vw, 1.5rem);
    line-height: 1.2em;
    color: var(--headerColor);
  }
  #cs-navigation .cs-li-link:before {
    position: absolute;
    bottom: -0.125rem;
    left: 0;
    /* active state underline */
    content: "";
    opacity: 1;
    display: none;
    background: currentColor;
    height: 1px;
    width: 100%;
  }
  #cs-navigation .cs-li-link.cs-active:before {
    display: block;
  }
  #cs-navigation .cs-button-solid {
    display: none;
  }
  #cs-navigation:before {
    z-index: -11;
    position: absolute;
    top: 100%;
    right: 0;
    /* black blurred overlay */
    content: "";
    opacity: 0;
    display: block;
    background: rgba(0, 0, 0, 0.6);
    height: 100vh;
    width: 0%;
    transition: width 0.5s, opacity 0.3s;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
  }
  #cs-navigation.cs-active .cs-ul-wrapper {
    opacity: 1;
    visibility: visible;
    transform: scaleX(1);
    transition-delay: 0.2s;
  }
  #cs-navigation.cs-active .cs-li {
    opacity: 1;
    transform: translateX(0);
  }
  #cs-navigation.cs-active:before {
    opacity: 1;
    width: 100%;
  }
}
/* Dark Mode */
@media only screen and (max-width: 63.9375rem) {
  body.dark-mode #cs-navigation {
    background-color: var(--dark);
  }
  body.dark-mode #cs-navigation .cs-logo {
    /* makes it white */
    filter: grayscale(1) brightness(1000%);
  }
  body.dark-mode #cs-navigation .cs-line {
    background-color: #fff;
  }
  body.dark-mode #cs-navigation .cs-ul-wrapper {
    background-color: var(--medium);
  }
  body.dark-mode #cs-navigation .cs-li-link {
    color: var(--bodyTextColorWhite);
  }
}
/*-- -------------------------- -->
<---     Navigation Dropdown    -->
<--- -------------------------- -*/
/* Mobile - 1023px */
@media only screen and (max-width: 63.9375rem) {
  #cs-navigation .cs-dropdown {
    position: relative;
    color: var(--bodyTextColorWhite);
  }
  #cs-navigation .cs-dropdown .cs-li-link {
    position: relative;
    transition: opacity 0.3s;
  }
  #cs-navigation .cs-dropdown.cs-active .cs-drop-ul {
    visibility: visible;
    opacity: 1;
    transform: scale(1);
    margin: 0.75rem 0 0 0;
    height: auto;
    padding: 1.5rem;
  }
  #cs-navigation .cs-dropdown.cs-active .cs-drop-link {
    opacity: 1;
  }
  #cs-navigation .cs-dropdown-button {
    border: none;
    background-color: transparent;
    font-family: inherit;
    font-size: clamp(1rem, 2.5vw, 1.5rem);
    cursor: pointer;
    -webkit-appearance: none;
       -moz-appearance: none;
            appearance: none;
  }
  #cs-navigation .cs-drop-icon {
    position: absolute;
    top: 50%;
    right: -1.25rem;
    transform: translateY(-50%);
    height: auto;
    width: 0.9375rem;
  }
  #cs-navigation .cs-drop-ul {
    visibility: hidden;
    opacity: 0;
    transform: scale(0);
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-end;
    margin: 0;
    background-color: var(--primary);
    height: 0;
    width: 100%;
    box-sizing: border-box;
    overflow: hidden;
    padding: 0 1.5rem 0 1.5rem;
    transition: padding 0.3s, margin 0.3s, height 0.3s, opacity 0.3s, transform 0.3s, visibility 0.3s;
    gap: 0.75rem;
    transform-origin: top right;
  }
  #cs-navigation .cs-drop-li {
    list-style: none;
    text-align: inherit;
  }
  #cs-navigation .cs-li-link.cs-drop-link {
    /* 14px - 16px */
    font-size: clamp(0.875rem, 2vw, 1.25rem);
    color: #fff;
  }
}
/* Desktop - 1024px */
@media only screen and (min-width: 64rem) {
  #cs-navigation .cs-dropdown {
    position: relative;
  }
  #cs-navigation .cs-dropdown.cs-active, #cs-navigation .cs-dropdown:hover {
    cursor: pointer;
  }
  #cs-navigation .cs-dropdown.cs-active .cs-drop-ul, #cs-navigation .cs-dropdown:hover .cs-drop-ul {
    visibility: visible;
    opacity: 1;
    transform: scaleY(1);
  }
  #cs-navigation .cs-dropdown.cs-active .cs-drop-li, #cs-navigation .cs-dropdown:hover .cs-drop-li {
    opacity: 1;
    transform: translateY(0);
  }
  #cs-navigation .cs-dropdown-button {
    border: none;
    background-color: transparent;
    font-family: inherit;
    font-size: inherit;
    cursor: pointer;
    -webkit-appearance: none;
       -moz-appearance: none;
            appearance: none;
  }
  #cs-navigation .cs-drop-icon {
    display: inline-block;
    height: auto;
    width: 0.9375rem;
  }
  #cs-navigation .cs-drop-ul {
    z-index: -100;
    /* if you have 8 or more links in your dropdown nav, uncomment the columns property to make the list into 2 even columns. Change it to 3 or 4 if you need extra columns. Then remove the transition delays on the cs-drop-li so they don't have weird scattered animations */
    position: absolute;
    top: 100%;
    visibility: hidden;
    opacity: 0;
    transform: scaleY(0);
    border-bottom: 5px solid var(--primary);
    margin: 0;
    background-color: #fff;
    min-width: 12.5rem;
    overflow: hidden;
    padding: 0;
    transition: transform 0.3s, visibility 0.3s, opacity 0.3s;
    box-shadow: rgba(149, 157, 165, 0.2) 0px 10px 16px;
    transform-origin: top;
  }
  #cs-navigation .cs-drop-li {
    opacity: 0;
    transform: translateY(-0.625rem);
    display: block;
    height: auto;
    width: 100%;
    list-style: none;
    text-decoration: none;
    font-size: 1rem;
    color: var(--bodyTextColor);
    transition: opacity 0.6s, transform 0.6s;
  }
  #cs-navigation .cs-drop-li:nth-of-type(1) {
    transition-delay: 0.05s;
  }
  #cs-navigation .cs-drop-li:nth-of-type(2) {
    transition-delay: 0.1s;
  }
  #cs-navigation .cs-drop-li:nth-of-type(3) {
    transition-delay: 0.15s;
  }
  #cs-navigation .cs-drop-li:nth-of-type(4) {
    transition-delay: 0.2s;
  }
  #cs-navigation .cs-drop-li:nth-of-type(5) {
    transition-delay: 0.25s;
  }
  #cs-navigation .cs-drop-li:nth-of-type(6) {
    transition-delay: 0.3s;
  }
  #cs-navigation .cs-drop-li:nth-of-type(7) {
    transition-delay: 0.35s;
  }
  #cs-navigation .cs-drop-li:nth-of-type(8) {
    transition-delay: 0.4s;
  }
  #cs-navigation .cs-drop-li:nth-of-type(9) {
    transition-delay: 0.45s;
  }
  #cs-navigation .cs-drop-li:nth-of-type(10) {
    transition-delay: 0.5s;
  }
  #cs-navigation .cs-drop-li:nth-of-type(11) {
    transition-delay: 0.55s;
  }
  #cs-navigation .cs-drop-li:nth-of-type(12) {
    transition-delay: 0.6s;
  }
  #cs-navigation .cs-drop-li:nth-of-type(13) {
    transition-delay: 0.65s;
  }
  #cs-navigation .cs-li-link.cs-drop-link {
    display: block;
    width: 100%;
    /* prevents padding from affecting height and width */
    box-sizing: border-box;
    padding: 0.75rem;
    white-space: nowrap;
    text-decoration: none;
    font-size: 1rem;
    line-height: 1.5em;
    color: var(--bodyTextColor);
    transition: color 0.3s, background-color 0.3s;
  }
  #cs-navigation .cs-li-link.cs-drop-link:hover {
    background-color: #f7f7f7;
  }
  #cs-navigation .cs-li-link.cs-drop-link:focus-visible {
    outline: 2px solid currentColor;
    outline-offset: -4px;
  }
  #cs-navigation .cs-li-link.cs-drop-link:before {
    display: none;
  }
}
/* Dark Mode */
@media only screen and (min-width: 0rem) {
  body.dark-mode #cs-navigation .cs-drop-ul {
    background-color: var(--dark);
  }
  body.dark-mode #cs-navigation .cs-li-link.cs-drop-link:hover {
    background-color: var(--medium);
  }
  body.dark-mode #cs-navigation .cs-drop-icon {
    /* turns it white */
    filter: grayscale(1) brightness(1000%);
  }
}
/*-- -------------------------- -->
<---     Desktop Navigation     -->
<--- -------------------------- -*/
/* Small Desktop - 1024px */
@media only screen and (min-width: 64rem) {
  #cs-navigation {
    z-index: 10000;
    position: fixed;
    background-color: #fff;
    width: 100%;
    /* prevents padding from affecting height and width */
    box-sizing: border-box;
    padding: 0 1rem;
    /* remove the font family so the Stitch inherits the fonts from your global stylesheet */
    font-family: "Maven Pro", "Arial", sans-serif;
    box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
  }
  #cs-navigation .cs-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin: auto;
    max-width: 80rem;
    width: 100%;
    gap: 1.5rem;
  }
  #cs-navigation .cs-toggle {
    display: none;
  }
  #cs-navigation .cs-logo {
    z-index: 100;
    display: flex;
    justify-content: center;
    align-items: center;
    /* margin-right auto pushes everything away from it to the right */
    margin: 0 auto 0 0;
    height: 4.0625rem;
    max-width: 21.875rem;
    width: 18.4%;
    padding: 0;
  }
  #cs-navigation .cs-logo img {
    height: 100%;
    width: 100%;
    /* ensures the image never overflows the container. It stays contained within it's width and height and expands to fill it then stops once it reaches an edge */
    -o-object-fit: contain;
       object-fit: contain;
  }
  #cs-navigation .cs-ul {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin: 0;
    width: 100%;
    padding: 0;
    /* 20px - 36px */
    gap: clamp(1.25rem, 2.6vw, 2.25rem);
  }
  #cs-navigation .cs-li {
    padding: 2rem 0;
    list-style: none;
    /* prevent flexbox from squishing it */
    flex: none;
  }
  #cs-navigation .cs-li-link {
    position: relative;
    display: block;
    margin: 0;
    text-decoration: none;
    /* 14px - 16px */
    font-size: clamp(0.875rem, 1vw, 1rem);
    line-height: 1.5em;
    color: var(--bodyTextColor);
  }
  #cs-navigation .cs-li-link:hover:before {
    width: 100%;
  }
  #cs-navigation .cs-li-link:before {
    position: absolute;
    bottom: 0rem;
    left: 0;
    /* active state underline */
    content: "";
    opacity: 1;
    display: block;
    background: var(--primary);
    height: 2px;
    width: 0%;
    transition: width 0.3s;
  }
  #cs-navigation .cs-li-link.cs-active:before {
    width: 100%;
  }
  #cs-navigation .cs-button-solid {
    color: #fff;
  }
}
/* Dark Mode */
@media only screen and (min-width: 64rem) {
  body.dark-mode #cs-navigation {
    background-color: var(--dark);
  }
  body.dark-mode #cs-navigation .cs-logo {
    /* makes it turn white */
    filter: grayscale(1) brightness(1000%);
  }
  body.dark-mode #cs-navigation .cs-li-link {
    color: var(--bodyTextColorWhite);
  }
  body.dark-mode #cs-navigation .cs-li-link:before {
    background-color: var(--primaryLight);
  }
}
/*-- -------------------------- -->
<---   Interior Page Header     -->
<--- -------------------------- -*/
/* Mobile */
@media only screen and (min-width: 0rem) {
  #int-hero {
    z-index: 1;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: 30vh;
    padding-top: 3.125rem;
  }
  #int-hero picture {
    z-index: -2;
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
  }
  #int-hero picture img {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    -o-object-fit: cover;
       object-fit: cover;
  }
  #int-hero h1 {
    position: relative;
    margin: 0 auto;
    margin-top: 4.375rem;
    margin-bottom: 1.875rem;
    max-width: 50rem;
    width: 96%;
    font-size: 2.1333333333rem;
    color: #fff;
    text-align: center;
  }
  #int-hero p {
    display: block;
    margin: auto;
    margin-bottom: 1.875rem;
    max-width: 25rem;
    width: 96%;
    color: #fff;
    text-align: center;
  }
  #int-hero:before {
    z-index: -1;
    position: absolute;
    top: 0;
    left: 0;
    content: "";
    opacity: 0.7;
    display: block;
    background: #000;
    height: 100%;
    width: 100%;
  }
}
/* Tablet */
@media only screen and (min-width: 48rem) {
  #int-hero {
    font-size: 100%;
  }
  #int-hero h1 {
    font-size: 2.75rem;
  }
}
/* Small Desktop */
@media only screen and (min-width: 64rem) {
  #int-hero {
    background-attachment: fixed;
    min-height: 9.375rem;
    height: auto;
    padding-top: 6.25rem;
    font-size: inherit;
    padding-block-end: 3.125rem;
  }
}
/*-- -------------------------- -->
<---            CTA             -->
<--- -------------------------- -*/
/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
  #cta-51 {
    padding: var(--sectionPadding);
    position: relative;
  }
  #cta-51 .cs-container {
    width: 100%;
    max-width: 80rem;
    margin: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    /* 48px - 64px */
    gap: clamp(3rem, 6vw, 4rem);
  }
  #cta-51 .cs-content {
    /* set text align to left if content needs to be left aligned */
    text-align: center;
    width: 100%;
    display: flex;
    flex-direction: column;
    /* centers content horizontally, set to flex-start to left align */
    align-items: center;
  }
  #cta-51 .cs-title {
    color: var(--bodyTextColorWhite);
  }
  #cta-51 .cs-text {
    margin-bottom: 1rem;
    color: var(--bodyTextColorWhite);
    opacity: 0.8;
  }
  #cta-51 .cs-text:last-of-type {
    margin-bottom: 2rem;
  }
  #cta-51 .cs-button-solid {
    font-size: 1rem;
    /* 46px - 56px */
    line-height: clamp(2.875rem, 5.5vw, 3.5rem);
    text-decoration: none;
    font-weight: 700;
    text-align: center;
    margin: 0;
    color: #fff;
    min-width: 9.375rem;
    padding: 0 1.5rem;
    background-color: var(--primary);
    border-radius: 0.25rem;
    display: inline-block;
    position: relative;
    z-index: 1;
    /* prevents padding from adding to the width */
    box-sizing: border-box;
  }
  #cta-51 .cs-button-solid:before {
    content: "";
    position: absolute;
    height: 100%;
    width: 0%;
    background: #000;
    opacity: 1;
    top: 0;
    left: 0;
    z-index: -1;
    border-radius: 0.25rem;
    transition: width 0.3s;
  }
  #cta-51 .cs-button-solid:hover:before {
    width: 100%;
  }
  #cta-51 .cs-picture {
    height: 100%;
    width: 100%;
    display: block;
    position: absolute;
    z-index: -1;
    top: 0;
    left: 0;
    z-index: -1;
  }
  #cta-51 .cs-picture:before {
    /* black color overlay */
    content: "";
    position: absolute;
    display: block;
    height: 100%;
    width: 100%;
    background: #000;
    opacity: 0.8;
    top: 0;
    left: 0;
    z-index: 1;
  }
  #cta-51 .cs-picture img {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    -o-object-fit: cover;
       object-fit: cover;
  }
}
/*-- -------------------------- -->
<---           Footer           -->
<--- -------------------------- -*/
/* Mobile */
@media only screen and (min-width: 0rem) {
  #footer {
    background: #1c1c1c;
    padding: 5.25rem 0 1.25rem;
    font-size: min(4vw, 1.2em);
  }
  #footer .left-section {
    margin: auto;
    margin-bottom: 3.125rem;
    text-align: center;
  }
  #footer .left-section .logo {
    display: inline-block;
    margin: auto;
    margin-bottom: 1.875em;
    height: 2em;
    width: auto;
    text-align: center;
  }
  #footer .left-section .logo img {
    display: block;
    height: 100%;
    width: auto;
  }
  #footer .left-section p {
    opacity: 0.9;
    margin: auto;
    width: 100%;
    font-size: 0.875rem;
    line-height: 1.9285714286rem;
    color: #fff;
    text-align: center;
  }
  #footer .right-section {
    margin: auto;
    max-width: 25.4375rem;
    width: 96%;
  }
  #footer .right-section .lists {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin: auto;
    max-width: 19.1875rem;
    width: 96%;
  }
  #footer .right-section .lists ul {
    margin-top: 0;
    margin-bottom: 0;
    padding: 0;
  }
  #footer .right-section .lists ul li {
    opacity: 0.9;
    margin-bottom: 0.75rem;
    list-style: none;
    font-size: 1.125rem;
    color: #fff;
  }
  #footer .right-section .lists ul li a {
    text-decoration: none;
    line-height: 1.1666666667em;
    color: #fff;
    transition: color 0.3s;
  }
  #footer .right-section .lists ul li a:hover {
    color: var(--primary);
  }
  #footer .right-section .lists ul h2 {
    position: relative;
    margin-bottom: 2.3125rem;
    text-transform: uppercase;
    font-size: 1.125rem;
    line-height: 1.1666666667em;
    font-weight: bold;
    color: #fff;
  }
  #footer .right-section .lists ul h2:before {
    position: absolute;
    bottom: -1rem;
    left: 0;
    content: "";
    opacity: 1;
    display: block;
    background: var(--primary);
    height: 0.125rem;
    width: 6.0625rem;
  }
  #footer .right-section .lists ul:nth-of-type(3) {
    margin-top: 3.125rem;
  }
  #footer .right-section .lists ul:nth-of-type(3) li {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 1rem;
  }
  #footer .right-section .lists ul:nth-of-type(3) li:last-of-type {
    margin-bottom: 0;
  }
  #footer .right-section .buttons {
    display: flex;
    justify-content: center;
  }
  #footer .right-section .cs-button-solid {
    margin: 0;
    height: 3.3125rem;
    width: 15.625rem;
    padding-top: 0.1875rem;
  }
  #footer .credit {
    margin: auto;
    margin-top: 6.25rem;
    width: 96%;
    font-size: 1rem;
    line-height: 2.25rem;
    color: #fff;
    text-align: center;
  }
  #footer .credit a {
    text-decoration: none;
    font-size: 1rem;
    color: var(--primary);
  }
  #footer .credit a:hover {
    text-decoration: underline;
  }
  #footer .credit .copyright {
    display: block;
    font-size: 1rem;
  }
}
/* Small Desktop */
@media only screen and (min-width: 64rem) {
  #footer {
    font-size: min(1.2vw, 1em);
  }
  #footer .container {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
    margin: auto;
    max-width: 82.5rem;
    width: 96%;
    padding: 0;
  }
  #footer .left-section {
    margin: 0;
    width: 25.5625rem;
    text-align: left;
  }
  #footer .left-section .logo {
    margin-left: 0;
    height: 2.5rem;
    text-align: left;
  }
  #footer .left-section p {
    margin-left: 0;
    text-align: left;
  }
  #footer .right-section {
    margin: 0;
    max-width: none;
    width: 38.0625rem;
  }
  #footer .right-section .lists {
    margin: 0;
    max-width: 48.0625rem;
    width: 96%;
  }
  #footer .right-section .lists ul li a {
    position: relative;
  }
  #footer .right-section .lists ul li a:before {
    position: absolute;
    bottom: -0.1875rem;
    left: 0;
    content: "";
    opacity: 1;
    display: block;
    background: var(--primary);
    height: 0.125rem;
    width: 0%;
    transition: width 0.3s;
  }
  #footer .right-section .lists ul li a:hover:before {
    width: 100%;
  }
  #footer .right-section .lists ul:nth-of-type(3) {
    margin-top: 0;
  }
  #footer .right-section .lists ul:nth-of-type(3) li:first-of-type {
    margin-bottom: 0;
  }
  #footer .right-section .buttons {
    justify-content: flex-start;
  }
  #footer .credit .copyright {
    display: inline-block;
  }
}
/* Dark Mode */
@media only screen and (min-width: 0rem) {
  body.dark-mode #footer {
    background: #061623;
  }
}/*# sourceMappingURL=root.css.map */