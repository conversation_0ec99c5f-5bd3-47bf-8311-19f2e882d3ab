.cities-sections {
  padding: 4rem 0;
  background-color: #ffffff;
  min-height: 100vh;
}

.state-section {
  margin-bottom: 3rem;
}

.state-name {
  text-align: center;
  margin: 2rem 0;
  font-size: 2rem;
  color: var(--primary, #333);
}

.cities-grid,
.additional-cities {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 1.5rem;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1rem;
  margin-top: 4rem;
  margin-bottom: 4rem;
}

@media (max-width: 1200px) {
  .cities-grid,
  .additional-cities {
    grid-template-columns: repeat(4, 1fr);
  }
}
@media (max-width: 992px) {
  .cities-grid,
  .additional-cities {
    grid-template-columns: repeat(3, 1fr);
  }
}
@media (max-width: 768px) {
  .cities-grid,
  .additional-cities {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (max-width: 480px) {
  .cities-grid,
  .additional-cities {
    grid-template-columns: 1fr;
  }
}
.additional-cities {
  display: none;
  margin-top: 0;
  margin-bottom: 0;
}

.city-link {
  background-color: #ffffff;
  border: 1px solid #eaeaea;
  border-radius: 8px;
  padding: 1.5rem;
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  text-decoration: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: inherit;
}

.city-link:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.city-link .city-name {
  color: var(--primary, #333);
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  line-height: 1.4;
  display: block;
}

.city-link .business-count {
  color: #666;
  font-size: 0.9rem;
  margin: 0;
  display: block;
}

.show-more-cities,
.hide-additional-cities {
  display: block;
  margin: 2rem auto;
  padding: 0.75rem 1.5rem;
  background-color: var(--primary, #007bff);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.show-more-cities:hover,
.hide-additional-cities:hover {
  background-color: --primary, #007bff;
}

@media (max-width: 768px) {
  .cities-sections {
    padding: 1rem;
  }
  .city-link {
    padding: 1.25rem;
  }
  .city-link .city-name {
    font-size: 1.2rem;
  }
} 

/*# sourceMappingURL=cities.css.map */
