---
title: "<PERSON> Arseneau Bookkeeping | Top Tax HQ"
description: "If you're a business owner losing countless hours dealing with your books, leave it to me"
preloadImg: "/assets/images/banner-sm.webp"
permalink: "mia-arseneau-bookkeeping/"
---

{% extends "layouts/base.html" %}

{% block head %}
    <link rel="stylesheet" href="/assets/css/listed.css" />
{% endblock %}

{% block body %}
    <section id="int-hero">
        <h1 id="home-h">Mia Arseneau Bookkeeping</h1>
        <picture>
            <source
                media="(max-width: 600px)"
                srcset="/assets/images/banner-sm.webp"
            />
            <source
                media="(min-width: 601px)"
                srcset="/assets/images/banner.webp"
            />
            <img
                aria-hidden="true"
                decoding="async"
                src="/assets/images/banner.webp"
                alt="accounting banner"
                loading="eager"
                width="2500"
                height="1667"
            />
        </picture>
    </section>

    <main class="business-listing-container">
        <section class="business-header">
            <div class="header-content">
                <div class="logo-container">
                    <img
                        src="https://images.squarespace-cdn.com/content/v1/67e59b2d65dd136989f280eb/f9925baa-110c-4b99-abb6-b77946379581/quickbooks-online-certification-level-2.png?format=300w"
                        alt="Mia Arseneau Bookkeeping Logo"
                        class="business-logo"
                        loading="lazy"
                        width="100"
                        height="100"
                    />
                </div>
                <div class="info-container">
                    <h2 class="business-title">Mia Arseneau Bookkeeping</h2>
                    <p class="category">Quickbooks, Bookkeeping, Payroll</p>
                </div>
            </div>
        </section>

        <section class="business-meta">
            <div class="contact-info-quick">
                <a href="tel:************" class="phone-link">(*************</a>
                <a
                    href="https://www.miaarseneaubookkeeping.com/"
                    target="_blank"
                    rel="noopener noreferrer"
                    class="website-link"
                    >Business Website</a
                >
            </div>
        </section>

        <div class="main-content-layout">
            <div class="content-left">
                <section class="location-hours-section card">
                    <h2>Location & Hours</h2>
                    <div class="location-hours-content">
                        <div class="map-container" id="map">
                            <iframe
                                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d358465.0401054551!2d-122.86316004999999!3d45.4192575!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0xa2bc84bbc3a682a5%3A0xf2932db88b22d3!2sMia%20Arseneau%20Bookkeeping!5e0!3m2!1sen!2sus!4v1745370193590!5m2!1sen!2sus"
                                width="600"
                                height="450"
                                style="border:0;"
                                allowfullscreen=""
                                loading="lazy"
                                referrerpolicy="no-referrer-when-downgrade"
                            ></iframe>
                        </div>
                        <div class="address-hours-info">
                            <div class="hours">
                                <ul>
                                    <li>
                                        Mon:
                                        <span class="time">Closed</span>
                                    </li>
                                    <li>
                                        Tue:
                                        <span class="time"
                                            >9:00 AM - 5:00 PM</span
                                        >
                                    </li>
                                    <li>
                                        Wed:
                                        <span class="time"
                                            >9:00 AM - 5:00 PM</span
                                        >
                                    </li>
                                    <li>
                                        Thu:
                                        <span class="time"
                                            >9:00 AM - 5:00 PM</span
                                        >
                                    </li>
                                    <li>
                                        Fri:
                                        <span class="time"
                                            >9:00 AM - 5:00 PM</span
                                        >
                                    </li>
                                    <li>
                                        Sat:
                                        <span class="time">
                                            <span class="time"
                                                >9:00 AM - 5:00 PM</span
                                            >
                                        </span>
                                    </li>
                                    <li>
                                        Sun: <span class="time">Closed</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </section>

                <section class="about-section card">
                    <h2>About the Business</h2>
                    <p>
                        If you're a business owner losing countless hours
                        dealing with your books, leave it to me! I'm a
                        QuickBooks certified bookkeeper in Sherwood, Oregon. I
                        would love to help your business garner an average of
                        $7,500 a year just by bringing me on board! Here's how:
                    </p>
                    <p>
                        - I'll keep your books accurate—when tax season rolls
                        around, you will have reduced your risk of penalties and
                        you won't miss out on any possible tax cuts. - I'll free
                        up 120+ of hours your time annually. With so much new
                        time on your hands, imagine how greatly you could expand
                        the revenue of your business! -I'll provide you with
                        monthly management reporting, customized for your needs.
                        You'll be empowered to make the best decisions for your
                        business based on reliable, relevant and timely
                        financial information. I want to help you achieve and
                        surpass your business goals. Let's connect!
                    </p>
                </section>
            </div>
            <aside class="content-right">
                <section class="highlights-section card">
                    <h2>Business Services</h2>
                    <ul>
                        <li>Precision Monthly Bookkeeping</li>
                        <li>Quickbooks Cleanup</li>
                        <li>Custom Management Reporting</li>
                        <li>Payroll Operation</li>
                    </ul>
                </section>

                <section class="service-area-section card">
                    <h2>Service Area</h2>
                    <p>Sherwood, OR</p>
                    <p>Portland, OR</p>
                    <p>Hillsboro, OR</p>
                    <p>McMinnville, OR</p>
                </section>
            </aside>
        </div>
        <section class="photo-gallery-section card">
            <h2>Photos</h2>

            <div class="photo-grid" id="photoGrid">
                <img
                    src="https://images.squarespace-cdn.com/content/v1/67e59b2d65dd136989f280eb/23a62d2d-c593-461a-ad6d-d2ac75c63ed5/4AFA1EBD-FF28-45D0-BB8C-252FFD6A0C51_1_201_a-min.jpg?format=2500w"
                    alt="Business photo 1"
                    loading="lazy"
                    decoding="async"
                    width="300"
                    height="200"
                />
                <img
                    src="https://images.squarespace-cdn.com/content/v1/67e59b2d65dd136989f280eb/f1fbbeb2-be25-4778-a8f3-17c98fbdf4c7/C28866A9-9961-4092-87EE-11AF8C791BD5_1_201_a-min.jpg?format=1000w"
                    alt="Business photo 2"
                    loading="lazy"
                    decoding="async"
                    width="300"
                    height="200"
                />
                <img
                    src="https://images.squarespace-cdn.com/content/v1/67e59b2d65dd136989f280eb/f9925baa-110c-4b99-abb6-b77946379581/quickbooks-online-certification-level-2.png?format=300w"
                    alt="Business photo 3"
                    loading="lazy"
                    decoding="async"
                    width="300"
                    height="200"
                />
                <img
                    src="https://images.squarespace-cdn.com/content/v1/67e59b2d65dd136989f280eb/740805b5-1817-4748-9c5e-36f0838e08ba/quickbooks-online-payroll-certification+%281%29.png?format=300w"
                    alt="Business photo 3"
                    loading="lazy"
                    decoding="async"
                    width="300"
                    height="200"
                />
            </div>
            <p class="no-photos-message">
                No photos available for this business.
            </p>
            <script>
                document.addEventListener("DOMContentLoaded", function () {
                    const photoGrid = document.getElementById("photoGrid");
                    const images = photoGrid.querySelectorAll("img");

                    if (images.length === 1) {
                        photoGrid.classList.add("single-photo");
                    } else if (images.length === 0) {
                        photoGrid.style.display = "none";
                        document.querySelector(
                            ".no-photos-message",
                        ).style.display = "block";
                    }
                });
            </script>
        </section>
    </main>

    {% include 'components/cta.html' %}
{% endblock %}
