---
title: "Erinn's Bookish Bookkeeping | Top Tax HQ"
description: "Let me get to know your wonderful business and invest the necessary amount of time and love into it to achieve the perfect happily ever after!"
preloadImg: "/assets/images/banner-sm.webp"
permalink: "erinns-bookish-bookkeeping/"
---

{% extends "layouts/base.html" %}

{% block head %}
    <link rel="stylesheet" href="/assets/css/listed.css" />
{% endblock %}

{% block body %}
    <!-- ============================================ -->
    <!--                    LANDING                   -->
    <!-- ============================================ -->

    <section id="int-hero">
        <h1 id="home-h">Erinn's Bookish Bookkeeping</h1>
        <picture>
            <source
                media="(max-width: 600px)"
                srcset="/assets/images/banner-sm.webp"
            />
            <source
                media="(min-width: 601px)"
                srcset="/assets/images/banner.webp"
            />
            <img
                aria-hidden="true"
                decoding="async"
                src="/assets/images/banner.webp"
                alt="accounting banner"
                loading="eager"
                width="2500"
                height="1667"
            />
        </picture>
    </section>

    <main class="business-listing-container">
        <section class="business-header">
            <div class="header-content">
                <div class="logo-container">
                    <img
                        src="https://lh3.googleusercontent.com/p/AF1QipNHauzaGQVJRsT3wVqqx0z0OzmDAeWM0Bdp-Lc5=s1360-w1360-h1020"
                        alt="Erinn's Bookish Bookkeeping Logo"
                        class="business-logo"
                        loading="lazy"
                        width="100"
                        height="100"
                    />
                </div>
                <div class="info-container">
                    <h2 class="business-title">Erinn's Bookish Bookkeeping</h2>
                    <p class="category">Bookkeeping, Payroll</p>
                </div>
            </div>
        </section>

        <section class="business-meta">
            <div class="contact-info-quick">
                <a href="tel:************" class="phone-link">(*************</a>
                <a
                    href="https://erinnsbookkeeping.com/"
                    target="_blank"
                    rel="noopener noreferrer"
                    class="website-link"
                    >Business Website</a
                >
            </div>
        </section>

        <div class="main-content-layout">
            <div class="content-left">
                <section class="location-hours-section card">
                    <h2>Location & Hours</h2>
                    <div class="location-hours-content">
                        <div class="map-container" id="map">
                            <iframe
                                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d47117.***********!2d-123.33694505!3d42.431396!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0xa7d5f623187a6a0f%3A0x53af0881ce63625d!2sErinn&#39;s%20Bookish%20Bookkeeping!5e0!3m2!1sen!2sus!4v1744936341336!5m2!1sen!2sus"
                                width="600"
                                height="450"
                                style="border:0;"
                                allowfullscreen=""
                                loading="lazy"
                                referrerpolicy="no-referrer-when-downgrade"
                            ></iframe>
                        </div>
                        <div class="address-hours-info">
                            <div class="hours">
                                <ul>
                                    <li>
                                        Mon:
                                        <span class="time"
                                            >9:00 AM - 5:00 PM</span
                                        >
                                    </li>
                                    <li>
                                        Tue:
                                        <span class="time"
                                            >9:00 AM - 5:00 PM</span
                                        >
                                    </li>
                                    <li>
                                        Wed:
                                        <span class="time"
                                            >9:00 AM - 5:00 PM</span
                                        >
                                    </li>
                                    <li>
                                        Thu:
                                        <span class="time"
                                            >9:00 AM - 5:00 PM</span
                                        >
                                    </li>
                                    <li>
                                        Fri:
                                        <span class="time"
                                            >9:00 AM - 5:00 PM</span
                                        >
                                    </li>
                                    <li>
                                        Sat: <span class="time">Closed</span>
                                    </li>
                                    <li>
                                        Sun: <span class="time">Closed</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </section>

                <section class="about-section card">
                    <h2>About the Business</h2>
                    <p>
                        Managing your business accounts no longer have to be a
                        dragon-sized trial to face. Let me get to know your
                        wonderful business and invest the necessary amount of
                        time and love into it to achieve the perfect happily
                        ever after!
                    </p>
                </section>
            </div>
            <aside class="content-right">
                <section class="highlights-section card">
                    <h2>Business Services</h2>
                    <ul>
                        <li>Invoicing</li>
                        <li>Profit and Loss reporting</li>
                        <li>Reconciling unkept books</li>
                        <li>Payroll + more!</li>
                    </ul>
                </section>

                <section class="service-area-section card">
                    <h2>Service Area</h2>
                    <p>Oregon, USA</p>
                </section>
            </aside>
        </div>
        <section class="photo-gallery-section card">
            <h2>Photos</h2>

            <div class="photo-grid" id="photoGrid">
                <img
                    src="https://lh3.googleusercontent.com/p/AF1QipNHauzaGQVJRsT3wVqqx0z0OzmDAeWM0Bdp-Lc5=s1360-w1360-h1020"
                    alt="Business photo 1"
                    loading="lazy"
                    decoding="async"
                    width="300"
                    height="200"
                />
                <img
                    src="https://www.google.com/maps/vt/data=hj6TlA93gZa-BIbsOx9BksvGMt3fh8cGQefsWPSZf9-13K-vsjb-z-_8YyHOafY_fuJ4wtscE2EIdE_kvcPg4udr3rAohMUEFqiIhPiYytmztlbXiL3BE2voNPrsgtu_0-Nvrn8QlV9pxvXKGx47tGP2btIN42BUdd31tU44Te7B0ETgTbOypcPP2bhtHrwX1D96PHw3HxjReHj_mBP8aAH-Jt79i9YnYDCuC8PttYQ9UG_u02wWtdsWJDCYTo5_sKBuyNtSGx0"
                    alt="Business photo 2"
                    loading="lazy"
                    decoding="async"
                    width="300"
                    height="200"
                />
            </div>
            <p class="no-photos-message">
                No photos available for this business.
            </p>
            <script>
                document.addEventListener("DOMContentLoaded", function () {
                    const photoGrid = document.getElementById("photoGrid");
                    const images = photoGrid.querySelectorAll("img");

                    if (images.length === 1) {
                        photoGrid.classList.add("single-photo");
                    } else if (images.length === 0) {
                        photoGrid.style.display = "none";
                        document.querySelector(
                            ".no-photos-message",
                        ).style.display = "block";
                    }
                });
            </script>
        </section>
    </main>

    {% include 'components/cta.html' %}
{% endblock %}
