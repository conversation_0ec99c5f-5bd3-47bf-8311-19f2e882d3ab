---
title: "All In Accounting Solutions, LLC | Top Tax HQ"
description: "All In Accounting Solutions offers the following services for you or your company needs, tax preparation and filing (e-filing)"
preloadImg: "/assets/images/banner-sm.webp"
permalink: "all-in-accounting-solutions-llc/"
---

{% extends "layouts/base.html" %}

{% block head %}
    <link rel="stylesheet" href="/assets/css/listed.css" />
{% endblock %}

{% block body %}
    <section id="int-hero">
        <h1 id="home-h">All In Accounting Solutions, LLC</h1>
        <picture>
            <source
                media="(max-width: 600px)"
                srcset="/assets/images/banner-sm.webp"
            />
            <source
                media="(min-width: 601px)"
                srcset="/assets/images/banner.webp"
            />
            <img
                aria-hidden="true"
                decoding="async"
                src="/assets/images/banner.webp"
                alt="accounting banner"
                loading="eager"
                width="2500"
                height="1667"
            />
        </picture>
    </section>

    <main class="business-listing-container">
        <section class="business-header">
            <div class="header-content">
                <div class="logo-container">
                    <img
                        src="https://lh3.googleusercontent.com/p/AF1QipOqBgm2XvQXAbTE1SuwAL92HymCtYRc3682dOqo=w408-h326-k-no"
                        alt="All In Accounting Solutions, LLC Logo"
                        class="business-logo"
                        loading="lazy"
                        width="100"
                        height="100"
                    />
                </div>
                <div class="info-container">
                    <h2 class="business-title">
                        All In Accounting Solutions, LLC
                    </h2>
                    <div class="rating-reviews">
                        <div class="rating">
                            <span class="stars" aria-label="5 out of 5 stars">
                                <span style="color: #f8ce0b;">★★★★★</span
                                ><span style="color: #e0e0e0;"></span>
                            </span>
                            <span class="rating-text">5.0</span>
                        </div>
                        <p class="review-count">(3 Reviews)</p>
                    </div>
                    <p class="category">
                        Tax Preparation, Bookkeeping, Payroll
                    </p>
                </div>
            </div>
        </section>

        <section class="business-meta">
            <div class="contact-info-quick">
                <a href="tel:************" class="phone-link">(*************</a>
                <a
                    href="https://allinaccountingsolutions.com/"
                    target="_blank"
                    rel="noopener noreferrer"
                    class="website-link"
                    >Business Website</a
                >
            </div>
        </section>

        <div class="main-content-layout">
            <div class="content-left">
                <section class="location-hours-section card">
                    <h2>Location & Hours</h2>
                    <div class="location-hours-content">
                        <div class="map-container" id="map">
                            <iframe
                                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2846.*************!2d-122.**************!3d44.**************!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0xa894a089b29e8431%3A0x6e0bee5604a53cfa!2sAll%20In%20Accounting%20Solutions%2C%20LLC!5e0!3m2!1sen!2sus!4v1744930470739!5m2!1sen!2sus"
                                width="600"
                                height="450"
                                style="border:0;"
                                allowfullscreen=""
                                loading="lazy"
                                referrerpolicy="no-referrer-when-downgrade"
                            ></iframe>
                        </div>
                        <div class="address-hours-info">
                            <div class="address">
                                <p>
                                    <strong>36939 Edgemont Dr</strong><br />
                                    Lebanon, OR 97355
                                </p>
                                <a
                                    href="https://maps.app.goo.gl/78Rrqrg3rr3LmCzz9"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    class="directions-link"
                                    >Get Directions</a
                                >
                            </div>
                            <div class="hours">
                                <ul>
                                    <li>
                                        Mon:
                                        <span class="time"
                                            >9:00 AM - 5:00 PM</span
                                        >
                                    </li>
                                    <li>
                                        Tue:
                                        <span class="time"
                                            >9:00 AM - 5:00 PM</span
                                        >
                                    </li>
                                    <li>
                                        Wed:
                                        <span class="time"
                                            >9:00 AM - 5:00 PM</span
                                        >
                                    </li>
                                    <li>
                                        Thu:
                                        <span class="time"
                                            >9:00 AM - 5:00 PM</span
                                        >
                                    </li>
                                    <li>
                                        Fri:
                                        <span class="time"
                                            >9:00 AM - 5:00 PM</span
                                        >
                                    </li>
                                    <li>
                                        Sat: <span class="time">Closed</span>
                                    </li>
                                    <li>
                                        Sun: <span class="time">Closed</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </section>

                <section class="about-section card">
                    <h2>About the Business</h2>
                    <p>
                        My goal is to make my client's lives easier. Currently,
                        tax law has become very intricate and can often lead to
                        confusion and overwhelming stress that business owners
                        do not necessarily have time to deal with. Businesses
                        should be putting effort in the growth of their company.
                        For individuals, tax law can be equally complicated.
                        Trying to determine the correct amount of tax liability
                        based on income, assets, etc. can be a hassle. Given my
                        background in both law (16 years) and accounting (4
                        years), I have what it takes to put your mind at ease. I
                        can utilize my education and experience to both help you
                        and your business. Further, I am authorized to represent
                        clients before the Internal Revenue Service and the
                        State Department to help resolve any issues that may
                        arise.
                    </p>
                    <p>
                        I am a hard-working, driven individual who is not afraid
                        to face a challenge. I am passionate about my work and
                        know how to get the job done. I would describe myself as
                        an open and honest person, with a high standard of
                        morals and ethics.
                    </p>
                </section>
            </div>
            <aside class="content-right">
                <section class="highlights-section card">
                    <h2>Business Services</h2>
                    <ul>
                        <li>
                            <a href="/tax-preparation/">Tax preparation</a> and
                            filing (e-filing)
                        </li>
                        <li>
                            Individual Income
                            <a href="/tax-preparation/">Tax Returns</a>
                        </li>
                        <li>
                            Partnership
                            <a href="/tax-preparation/">Tax Returns</a>
                        </li>
                        <li>
                            Corporate
                            <a href="/tax-preparation/">Tax Returns</a>
                        </li>
                        <li>
                            Estate <a href="/tax-preparation/">Tax Returns</a>
                        </li>
                        <li>
                            <a href="/bookkeeping/">Bookkeeping Services</a>
                        </li>
                        <li>
                            <a href="/payroll-services/">Payroll Services</a>
                            (including quarterly and year-end tax filings)
                        </li>
                    </ul>
                </section>

                <section class="service-area-section card">
                    <h2>Service Area</h2>
                    <p>Lebanon, OR</p>
                </section>
            </aside>
        </div>
        <section class="photo-gallery-section card">
            <h2>Photos</h2>

            <div class="photo-grid" id="photoGrid">
                <img
                    src="https://images.unsplash.com/photo-1544761634-dc512f2238a3?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=************************************************************************&ixlib=rb-4.0.3&q=80&w=1080"
                    alt="Business photo 1"
                    loading="lazy"
                    decoding="async"
                    width="300"
                    height="200"
                />
                <img
                    src="https://images.unsplash.com/photo-1551806235-6692cbfc690b?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=******************************************************************&ixlib=rb-4.0.3&q=80&w=1080"
                    alt="Business photo 2"
                    loading="lazy"
                    decoding="async"
                    width="300"
                    height="200"
                />
                <img
                    src="https://bc-user-uploads.brandcrowd.com/public/media-Production/d16f8fe0-93e9-4dd2-9d30-73d31d39a460/34b2be3b-bbaf-4e8c-9fa0-078830a24ee0_2x"
                    alt="Business photo 3"
                    loading="lazy"
                    decoding="async"
                    width="300"
                    height="200"
                />
            </div>
            <p class="no-photos-message">
                No photos available for this business.
            </p>
            <script>
                document.addEventListener("DOMContentLoaded", function () {
                    const photoGrid = document.getElementById("photoGrid");
                    const images = photoGrid.querySelectorAll("img");

                    if (images.length === 1) {
                        photoGrid.classList.add("single-photo");
                    } else if (images.length === 0) {
                        photoGrid.style.display = "none";
                        document.querySelector(
                            ".no-photos-message",
                        ).style.display = "block";
                    }
                });
            </script>
        </section>
    </main>

    {% include 'components/cta.html' %}
{% endblock %}
