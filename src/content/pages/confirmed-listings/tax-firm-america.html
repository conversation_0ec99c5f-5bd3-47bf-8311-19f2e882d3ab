---
title: "Tax Firm America | Top Tax HQ"
description: "Tax Firm specializes in accounting, payrolls, and tax returns for individuals and businesses. We help clients in all different industries at all different levels."
preloadImg: "/assets/images/banner-sm.webp"
permalink: "tax-firm-america/"
---

{% extends "layouts/base.html" %}

{% block head %}
    <link rel="stylesheet" href="/assets/css/listed.css" />
{% endblock %}

{% block body %}
    <section id="int-hero">
        <h1 id="home-h">Tax Firm America</h1>
        <picture>
            <source
                media="(max-width: 600px)"
                srcset="/assets/images/banner-sm.webp"
            />
            <source
                media="(min-width: 601px)"
                srcset="/assets/images/banner.webp"
            />
            <img
                aria-hidden="true"
                decoding="async"
                src="/assets/images/banner.webp"
                alt="accounting banner"
                loading="eager"
                width="2500"
                height="1667"
            />
        </picture>
    </section>

    <main class="business-listing-container">
        <section class="business-header">
            <div class="header-content">
                <div class="logo-container">
                    <img
                        src="https://static.wixstatic.com/media/e6c96b_9a5701cb7b46478284a5c18f9c561457~mv2.png/v1/fill/w_188,h_178,al_c,q_85,usm_0.66_1.00_0.01,enc_avif,quality_auto/logo%20Tax%20Firm%20America%20transparent.png"
                        alt="Tax Firm America Logo"
                        class="business-logo"
                        loading="lazy"
                        width="100"
                        height="100"
                    />
                </div>
                <div class="info-container">
                    <h2 class="business-title">
                        Tax Firm America
                    </h2>
                    <p class="category">
                        Accounting, Payroll, Business Tax
                    </p>
                </div>
            </div>
        </section>

        <section class="business-meta">
            <div class="contact-info-quick">
                <a href="tel:(*************" class="phone-link"
                    >(*************</a
                >
                <a
                    href="https://www.taxfirmamerica.com/"
                    target="_blank"
                    rel="noopener noreferrer"
                    class="website-link"
                    >Business Website</a
                >
            </div>
        </section>

        <div class="main-content-layout">
            <div class="content-left">
                <section class="location-hours-section card">
                    <h2>Location & Hours</h2>
                    <div class="location-hours-content">
                        <div class="map-container" id="map">
                            <iframe
                                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3580.9599123764724!2d-80.17420249999999!3d26.165435000000002!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x88d90193ab0d60fd%3A0xcfd5ade5f19fa98d!2sTax%20Firm%20America!5e0!3m2!1sen!2sus!4v1750288107778!5m2!1sen!2sus"
                                width="600"
                                height="450"
                                style="border:0;"
                                allowfullscreen=""
                                loading="lazy"
                                referrerpolicy="no-referrer-when-downgrade"
                            ></iframe>
                        </div>
                        <div class="address-hours-info">
                            <div class="address">
                                <p>
                                    <strong>W Oakland Park Blvd</strong><br />
                                    Fort Lauderdale, FL 33311
                                </p>
                                <a
                                    href="https://maps.app.goo.gl/JpPLrDkjTkBwnat87"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    class="directions-link"
                                    >Get Directions</a
                                >
                            </div>
                            <div class="hours">
                                <ul>
                                    <li>
                                        Mon:
                                        <span class="time"
                                            >9:00 AM - 7:00 PM</span
                                        >
                                    </li>
                                    <li>
                                        Tue:
                                        <span class="time"
                                            >9:00 AM - 7:00 PM</span
                                        >
                                    </li>
                                    <li>
                                        Wed:
                                        <span class="time"
                                            >9:00 AM - 7:00 PM</span
                                        >
                                    </li>
                                    <li>
                                        Thu:
                                        <span class="time"
                                            >9:00 AM - 7:00 PM</span
                                        >
                                    </li>
                                    <li>
                                        Fri:
                                        <span class="time"
                                            >9:00 AM - 7:00 PM</span
                                        >
                                    </li>
                                    <li>
                                        Sat: <span class="time">Closed</span>
                                    </li>
                                    <li>
                                        Sun: <span class="time">Closed</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </section>

                <section class="about-section card">
                    <h2>About the Business</h2>
                    <p>
                      International Accounting and Taxes
                      Accounting -  Payroll - Payroll tax - Business Tax - Individual Tax - Sales tax - Business creation - Due diligence - Business plan 
                      30 years of Experience 
                      We speak French and English
                    </p>
                    <p>
                      Tax Firm specializes in accounting, payrolls, and tax returns for individuals and businesses. We help clients in all different industries at all different levels
                    </p>
                </section>
            </div>
            <aside class="content-right">
                <section class="highlights-section card">
                    <h2>Business Services</h2>
                    <ul>
                      <li>Bank reconciliation</li>
                      <li>Generate reports and analyze financial data</li>
                      <li>Evaluate cash flow and improve profitability</li>
                      <li>Prepare budgets</li>
                      <li>Strategic tax planning and financial forecasting</li>
                      <li>Accounting systems set-up</li>
                      <li>Chart of Accounts and General Ledger</li>
                      <li>Internal controls for fraud prevention</li>
                      <li>Quarterly, monthly, and annual financial statements</li>
                      <li>Use and sales tax processing</li>
                    </ul>
                </section>

                <section class="service-area-section card">
                    <h2>Service Area</h2>
                    <p>Fort Lauderdale, FL</p>
                </section>
            </aside>
        </div>
        <section class="photo-gallery-section card">
            <h2>Photos</h2>

            <div class="photo-grid" id="photoGrid">
                <img
                    src="https://static.wixstatic.com/media/e6c96b_9a5701cb7b46478284a5c18f9c561457~mv2.png/v1/fill/w_188,h_178,al_c,q_85,usm_0.66_1.00_0.01,enc_avif,quality_auto/logo%20Tax%20Firm%20America%20transparent.png"
                    alt="Business photo 1"
                    loading="lazy"
                    decoding="async"
                    width="300"
                    height="200"
                />
                <img
                    src="https://lh3.googleusercontent.com/p/AF1QipOS5nsohqOHnL6nzrS9RqS-1hFp5Me-XbgtnNny=s1360-w1360-h1020-rw"
                    alt="Business photo 2"
                    loading="lazy"
                    decoding="async"
                    width="300"
                    height="200"
                />
                <img
                    src="https://lh3.googleusercontent.com/p/AF1QipPS_lXFG2AFcSvmsK3J19_FBq3knHZ44ALSDSsB=s1360-w1360-h1020-rw"
                    alt="Business photo 3"
                    loading="lazy"
                    decoding="async"
                    width="300"
                    height="200"
                />
                <img
                    src="https://static.wixstatic.com/media/e6c96b_fe74490bf5904ba992fcd5011f26641d~mv2.png/v1/fill/w_398,h_266,al_c,q_85,usm_0.66_1.00_0.01,enc_avif,quality_auto/notary%20logo.png"
                    alt="Business photo 4"
                    loading="lazy"
                    decoding="async"
                    width="300"
                    height="200"
                />
            </div>
            <p class="no-photos-message">
                No photos available for this business.
            </p>
            <script>
                document.addEventListener("DOMContentLoaded", function () {
                    const photoGrid = document.getElementById("photoGrid");
                    const images = photoGrid.querySelectorAll("img");

                    if (images.length === 1) {
                        photoGrid.classList.add("single-photo");
                    } else if (images.length === 0) {
                        photoGrid.style.display = "none";
                        document.querySelector(
                            ".no-photos-message",
                        ).style.display = "block";
                    }
                });
            </script>
        </section>
    </main>

    {% include 'components/cta.html' %}
{% endblock %}
