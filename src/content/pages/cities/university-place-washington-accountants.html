---
title: "Best Accountants in University Place, Washington | Top Tax HQ"
description: "Find the best Accountant in University Place, Washington. Browse our comprehensive list of accounting professionals with ratings and reviews."
preloadImg: "/assets/images/banner-sm.webp"
permalink: "university-place-washington-accountants/"
eleventyNavigation:
    hideOnMobile: true
    hideOnDesktop: true
    key: University Place
    order: 3001
---

{% extends "layouts/base.html" %}

{% block head %}
    <link rel="stylesheet" href="/assets/css/city.css" />
    <style>
        .business-card img {
            max-width: 100%;
            height: auto;
            object-fit: cover;
        }
    </style>
{% endblock %}

{% block body %}

<section id="int-hero">
    <h1 id="home-h">Best Accountants in University Place, Washington</h1>
    <picture>
        <source media="(max-width: 600px)" srcset="/assets/images/banner-sm.webp" />
        <source media="(min-width: 601px)" srcset="/assets/images/banner.webp" />
        <img src="/assets/images/banner.webp" alt="accountant banner" loading="eager" width="2500" height="1667" />
    </picture>
</section>

<section class="business-listings">
    <div class="business-grid">
<div class="business-card">
                <div class="business-image">
                    <img src="https://lh5.googleusercontent.com/p/AF1QipMNY5WjMYhUZau_mGjXKpE9EXChxpUt4KcjsalO=w800-h500-k-no" alt="253 Bookkeeping" loading="lazy" />
                </div>
                <h3>253 Bookkeeping</h3>
                <div class="rating">
                    <span class="stars">★★★★★</span>
                    <span class="review-count">(55 reviews)</span>
                </div>
                <p class="address">Bookkeeping service</p>
                <p class="address">University Place, WA 98467</p>
                <p class="phone">******-258-6533</p>
                <a href="/no-website" class="view-website-btn">View Website</a>
            </div><div class="business-card">
                <div class="business-image">
                    <img src="https://lh5.googleusercontent.com/p/AF1QipOweqyVgtUSJb4uqbLyTdtZD6yEIGdFbniELQ0f=w800-h500-k-no" alt="L.C. Miller CPA, PS" loading="lazy" />
                </div>
                <h3>L.C. Miller CPA, PS</h3>
                <div class="rating">
                    <span class="stars">★★★★★</span>
                    <span class="review-count">(41 reviews)</span>
                </div>
                <p class="address">Tax preparation service</p>
                <p class="address">4007 Bridgeport Way W # F1, University Place, WA 98466</p>
                <p class="phone">******-565-9424</p>
                <a href="/no-website" class="view-website-btn">View Website</a>
            </div><div class="business-card">
                <div class="business-image">
                    <img src="https://streetviewpixels-pa.googleapis.com/v1/thumbnail?panoid=wckHE7OajkXk8pIvhklApQ&cb_client=search.gws-prod.gps&w=800&h=500&yaw=322.44638&pitch=0&thumbfov=100" alt="Tax Center & Accounting, LLC" loading="lazy" />
                </div>
                <h3>Tax Center & Accounting, LLC</h3>
                <div class="rating">
                    <span class="stars">★★★</span>
                    <span class="review-count">(11 reviews)</span>
                </div>
                <p class="address">Accountant</p>
                <p class="address">7621 27th St W Ste 1A, University Place, WA 98466</p>
                <p class="phone">******-565-5637</p>
                <a href="/no-website" class="view-website-btn">View Website</a>
            </div><div class="business-card">
                <div class="business-image">
                    <img src="https://lh5.googleusercontent.com/p/AF1QipMzLNfzjo9BghFHF7n_blDfiwNkVYIIBKDBao2G=w800-h500-k-no" alt="Sound Advisors" loading="lazy" />
                </div>
                <h3>Sound Advisors</h3>
                <div class="rating">
                    <span class="stars">★★★★½</span>
                    <span class="review-count">(10 reviews)</span>
                </div>
                <p class="address">Tax preparation service</p>
                <p class="address">2518 Bridgeport Way W, University Place, WA 98466</p>
                <p class="phone">******-466-3769</p>
                <a href="/no-website" class="view-website-btn">View Website</a>
            </div><div class="business-card">
                <div class="business-image">
                    <img src="https://lh5.googleusercontent.com/p/AF1QipOa_K-sFzpFqvTgNqFbI-8AzXVNyk-8dpEU_xQ8=w800-h500-k-no" alt="Kelly Company Tax Preparation" loading="lazy" />
                </div>
                <h3>Kelly Company Tax Preparation</h3>
                <div class="rating">
                    <span class="stars">★★★★★</span>
                    <span class="review-count">(9 reviews)</span>
                </div>
                <p class="address">Tax preparation</p>
                <p class="address">5229 53rd St W, University Place, WA 98467</p>
                <p class="phone">******-473-2867</p>
                <a href="/no-website" class="view-website-btn">View Website</a>
            </div><div class="business-card">
                <div class="business-image">
                    <img src="https://lh5.googleusercontent.com/p/AF1QipPbW16DUQJaNfx-9A5zD4EcHqkVQ1xIFpxp8a9D=w800-h500-k-no" alt="Commencement Bay CPA PLLC" loading="lazy" />
                </div>
                <h3>Commencement Bay CPA PLLC</h3>
                <div class="rating">
                    <span class="stars">★★★★½</span>
                    <span class="review-count">(8 reviews)</span>
                </div>
                <p class="address">Certified public accountant</p>
                <p class="address">3318 Bridgeport Way W, University Place, WA 98466</p>
                <p class="phone">******-987-1770</p>
                <a href="/no-website" class="view-website-btn">View Website</a>
            </div><div class="business-card">
                <div class="business-image">
                    <img src="https://streetviewpixels-pa.googleapis.com/v1/thumbnail?panoid=uO3RbFS-BhhMfb0ydR4mNQ&cb_client=search.gws-prod.gps&w=800&h=500&yaw=69.28244&pitch=0&thumbfov=100" alt="North Financial Inc" loading="lazy" />
                </div>
                <h3>North Financial Inc</h3>
                <div class="rating">
                    <span class="stars">★★★★½</span>
                    <span class="review-count">(8 reviews)</span>
                </div>
                <p class="address">Tax preparation service</p>
                <p class="address">4103 Bridgeport Way W STE C, University Place, WA 98466</p>
                <p class="phone">******-460-3541</p>
                <a href="/no-website" class="view-website-btn">View Website</a>
            </div><div class="business-card">
                <div class="business-image">
                    <img src="https://streetviewpixels-pa.googleapis.com/v1/thumbnail?panoid=P4s3cc7W4jcQNAf7IdHTYg&cb_client=search.gws-prod.gps&w=800&h=500&yaw=247.83925&pitch=0&thumbfov=100" alt="Kevin Iverson CPA Inc." loading="lazy" />
                </div>
                <h3>Kevin Iverson CPA Inc.</h3>
                <div class="rating">
                    <span class="stars">★★★★</span>
                    <span class="review-count">(7 reviews)</span>
                </div>
                <p class="address">Certified public accountant</p>
                <p class="address">5350 Orchard St W # 201, University Place, WA 98467</p>
                <p class="phone">******-476-5790</p>
                <a href="/no-website" class="view-website-btn">View Website</a>
            </div><div class="business-card">
                <div class="business-image">
                    <img src="https://lh5.googleusercontent.com/p/AF1QipMoLyCpDihOzaFk5OJEUtPf_sUXI42L_2emF5df=w800-h500-k-no" alt="DANIEL M. GILL, CPA/P.S." loading="lazy" />
                </div>
                <h3>DANIEL M. GILL, CPA/P.S.</h3>
                <div class="rating">
                    <span class="stars">★★★★★</span>
                    <span class="review-count">(5 reviews)</span>
                </div>
                <p class="address">Certified public accountant</p>
                <p class="address">2518 Bridgeport Way W, University Place, WA 98466</p>
                <p class="phone">******-565-6766</p>
                <a href="/no-website" class="view-website-btn">View Website</a>
            </div><div class="business-card">
                <div class="business-image">
                    <img src="https://streetviewpixels-pa.googleapis.com/v1/thumbnail?panoid=3Yk9HIp2NSYgacVQBXznrw&cb_client=search.gws-prod.gps&w=800&h=500&yaw=93.540726&pitch=0&thumbfov=100" alt="Messina Frank J CPA" loading="lazy" />
                </div>
                <h3>Messina Frank J CPA</h3>
                <div class="rating">
                    <span class="stars">★★★★★</span>
                    <span class="review-count">(4 reviews)</span>
                </div>
                <p class="address">Accountant</p>
                <p class="address">2601 70th Ave W, University Place, WA 98466</p>
                <p class="phone">******-565-2626</p>
                <a href="/no-website" class="view-website-btn">View Website</a>
            </div><div class="business-card">
                <div class="business-image">
                    <img src="https://streetviewpixels-pa.googleapis.com/v1/thumbnail?panoid=LK3Vmlmi5T_1K_7Ht-aicQ&cb_client=search.gws-prod.gps&w=800&h=500&yaw=259.9164&pitch=0&thumbfov=100" alt="Mickelson & Co" loading="lazy" />
                </div>
                <h3>Mickelson & Co</h3>
                <div class="rating">
                    <span class="stars">★★★★★</span>
                    <span class="review-count">(4 reviews)</span>
                </div>
                <p class="address">Accountant</p>
                <p class="address">2518 Bridgeport Way W, University Place, WA 98466</p>
                <p class="phone">******-565-6766</p>
                <a href="/no-website" class="view-website-btn">View Website</a>
            </div><div class="business-card">
                <div class="business-image">
                    <img src="https://streetviewpixels-pa.googleapis.com/v1/thumbnail?panoid=8alw6JX8pYzsHERXnGoQiQ&cb_client=search.gws-prod.gps&w=800&h=500&yaw=180.81815&pitch=0&thumbfov=100" alt="Fred Axe CPA - Axe's Taxes, LLC" loading="lazy" />
                </div>
                <h3>Fred Axe CPA - Axe's Taxes, LLC</h3>
                <div class="rating">
                    <span class="stars">★★★</span>
                    <span class="review-count">(4 reviews)</span>
                </div>
                <p class="address">Accountant</p>
                <p class="address">7024 27th St W Suite B, University Place, WA 98466</p>
                <p class="phone">******-297-4443</p>
                <a href="/no-website" class="view-website-btn">View Website</a>
            </div><div class="business-card">
                <div class="business-image">
                    <img src="https://streetviewpixels-pa.googleapis.com/v1/thumbnail?panoid=g1R6pe83tInDLCynFVdehg&cb_client=search.gws-prod.gps&w=800&h=500&yaw=203.13705&pitch=0&thumbfov=100" alt="Chieko Maclean, CPA" loading="lazy" />
                </div>
                <h3>Chieko Maclean, CPA</h3>
                <div class="rating">
                    <span class="stars">★★★★★</span>
                    <span class="review-count">(1 reviews)</span>
                </div>
                <p class="address">Accountant</p>
                <p class="address">7406 27th St W #301, University Place, WA 98466</p>
                <p class="phone">******-722-9938</p>
                <a href="/no-website" class="view-website-btn">View Website</a>
            </div><div class="business-card">
                <div class="business-image">
                    <img src="https://streetviewpixels-pa.googleapis.com/v1/thumbnail?panoid=p7IScDzOCjCrx5-j4UPnlg&cb_client=search.gws-prod.gps&w=800&h=500&yaw=94.3496&pitch=0&thumbfov=100" alt="Doria & Associates" loading="lazy" />
                </div>
                <h3>Doria & Associates</h3>
                <div class="rating">
                    <span class="stars">★★★★★</span>
                    <span class="review-count">(1 reviews)</span>
                </div>
                <p class="address">Tax preparation service</p>
                <p class="address">2601 70th Ave W, University Place, WA 98466</p>
                <p class="phone">******-756-8868</p>
                <a href="/no-website" class="view-website-btn">View Website</a>
            </div><div class="business-card">
                <div class="business-image">
                    <img src="https://streetviewpixels-pa.googleapis.com/v1/thumbnail?panoid=CqeWVL1C7SpLKfQ92aPn5Q&cb_client=search.gws-prod.gps&w=800&h=500&yaw=117.59422&pitch=0&thumbfov=100" alt="Michaels & Associates" loading="lazy" />
                </div>
                <h3>Michaels & Associates</h3>
                <div class="rating">
                    <span class="stars">★★★★★</span>
                    <span class="review-count">(1 reviews)</span>
                </div>
                <p class="address">Accounting firm</p>
                <p class="address">6824 19th St W #265, University Place, WA 98466</p>
                <p class="phone">******-254-6098</p>
                <a href="/no-website" class="view-website-btn">View Website</a>
            </div>
    </div>
</section>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const itemsPerPage = 12;
        const businessCards = document.querySelectorAll('.business-card');
        const totalItems = businessCards.length;
        const totalPages = Math.ceil(totalItems / itemsPerPage);
        let currentPage = 1;

        function showPage(page) {
            const start = (page - 1) * itemsPerPage;
            const end = start + itemsPerPage;

            businessCards.forEach((card, index) => {
                card.style.display = index >= start && index < end ? 'block' : 'none';
            });

            document.querySelectorAll('.page-link').forEach(link => {
                link.classList.toggle('active', parseInt(link.textContent) === page);
            });
        }

        function createPagination() {
            const pagination = document.createElement('div');
            pagination.className = 'pagination';

            for (let i = 1; i <= totalPages; i++) {
                const pageLink = document.createElement('a');
                pageLink.className = 'page-link';
                pageLink.textContent = i;
                pageLink.href = '#';
                pageLink.onclick = (e) => {
                    e.preventDefault();
                    currentPage = i;
                    showPage(currentPage);
                };
                pagination.appendChild(pageLink);
            }
            document.querySelector('.business-grid').after(pagination);
        }

        createPagination();
        showPage(1);
    });
</script>

{% endblock %}